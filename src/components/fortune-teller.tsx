
"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { generateLabubuImage } from "@/ai/flows/generate-labubu-image";
import LabubuCard from "./labubu-card";
import TypingAnimation from "./typing-animation";
import { Wand<PERSON><PERSON><PERSON>, Settings, Star } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { DataManager } from "@/lib/data-manager";
import { BackgroundManager } from "@/lib/background-manager";
import { Persona, Background } from "@/types/labubu";
import SettingsDialog from "./settings/settings-dialog";

export default function FortuneTeller() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isRevealed, setIsRevealed] = useState(false);
  const [currentImage, setCurrentImage] = useState({ url: "", hint: "" });
  const [fortune, setFortune] = useState("");
  const [currentPersona, setCurrentPersona] = useState<Persona | null>(null);
  const [currentBackground, setCurrentBackground] = useState<Background | null>(null);
  const [selectedFacts, setSelectedFacts] = useState<string[]>([]);
  const { toast } = useToast();

  const handleGenerate = async () => {
    if (isGenerating) return;

    // Get random persona and background
    const persona = DataManager.getRandomPersona();
    const background = BackgroundManager.getRandomBackground();

    if (!persona) {
      toast({
        title: "Setup Required",
        description: "Please add at least one persona in settings.",
        variant: "destructive",
      });
      return;
    }

    // Select random facts from the persona
    const facts = DataManager.selectRandomFacts(persona);

    // Reset and start
    setIsGenerating(true);
    setIsRevealed(false);
    setFortune("");
    setCurrentPersona(persona);
    setCurrentBackground(background);
    setSelectedFacts(facts);

    // Set initial placeholder image
    setCurrentImage({
      url: "/placeholder-labubu.svg",
      hint: `${persona.name} in ${background.name}`,
    });

    // After card flip animation (1s), start generation
    setTimeout(async () => {
      setIsRevealed(true); // Trigger slide and show fortune area

      try {
        const result = await generateLabubuImage({
          persona,
          background,
          selectedFacts: facts,
        });

        // Update with generated image and text
        setCurrentImage({
          url: result.imageUrl,
          hint: `${persona.name} in ${background.name}`,
        });
        setFortune(result.text);
        setIsGenerating(false);
      } catch (error) {
        console.error("Error generating Labubu:", error);
        toast({
          title: "Generation Failed",
          description: "Could not generate your unique Labubu. Please try again.",
          variant: "destructive",
        });
        setFortune("The magical energies are unstable... please try again.");
        setIsGenerating(false);
      }
    }, 1000); // Duration of the card flip animation
  };

  const hasCard = currentImage.url !== "";

  return (
    <div className="flex flex-col items-center gap-8 w-full max-w-5xl">
      <div className="text-center space-y-2 relative">
        <div className="absolute top-0 right-0">
          <SettingsDialog>
            <Button variant="outline" size="icon">
              <Settings className="h-4 w-4" />
            </Button>
          </SettingsDialog>
        </div>
        <h1 className="text-5xl md:text-7xl font-headline font-bold text-slate-800 tracking-tighter">
          Lucky Labubu
        </h1>
        <p className="text-lg text-muted-foreground max-w-md mx-auto">
          Generate a unique Labubu with magical personas and rare backgrounds.
          Each creation is one-of-a-kind!
        </p>
        {currentPersona && currentBackground && (
          <div className="text-sm text-muted-foreground space-y-1">
            <p>
              Persona: <span className="font-medium">{currentPersona.name}</span>
            </p>
            <p className="flex items-center justify-center gap-1">
              Background: <span className="font-medium">{currentBackground.name}</span>
              <span
                className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                style={{
                  backgroundColor: BackgroundManager.getRarityColor(currentBackground.rarity) + '20',
                  color: BackgroundManager.getRarityColor(currentBackground.rarity)
                }}
              >
                <Star className="w-3 h-3" />
                {BackgroundManager.getRarityDisplayName(currentBackground.rarity)}
              </span>
            </p>
          </div>
        )}
      </div>

      <div
        className="relative w-full flex items-center justify-center"
        style={{ height: "600px" }}
      >
        <div
          className={cn(
            "perspective w-[400px] h-[600px] transition-all duration-1000 ease-in-out",
            // Move left on reveal, but only on desktop
            isRevealed ? "md:-translate-x-1/3" : "translate-x-0",
            // Fade in/out
            hasCard ? "opacity-100" : "opacity-0 scale-95"
          )}
        >
          <LabubuCard
            imageUrl={currentImage.url}
            hint={currentImage.hint}
            isFlipping={hasCard}
          />
        </div>

        <div
          className={cn(
            "absolute w-full md:w-1/2 p-4 top-1/2 -translate-y-1/2 right-0 md:translate-x-1/3 transition-opacity duration-700",
            isRevealed && fortune
              ? "opacity-100 delay-500"
              : "opacity-0 pointer-events-none"
          )}
        >
          {fortune && (
            <div className="bg-card p-6 rounded-2xl shadow-lg w-full max-w-md mx-auto text-left space-y-4">
              <h3 className="font-bold text-xl mb-3 text-gray-700">
                A Message From The Ether...
              </h3>
              <TypingAnimation
                text={fortune}
                className="text-lg text-gray-600"
              />
              {selectedFacts.length > 0 && (
                <div className="border-t pt-4">
                  <h4 className="font-semibold text-sm text-gray-600 mb-2">
                    Special Traits Revealed:
                  </h4>
                  <div className="space-y-1">
                    {selectedFacts.map((fact, index) => (
                      <p key={index} className="text-sm text-gray-600">
                        • {fact}
                      </p>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <Button
        onClick={handleGenerate}
        disabled={isGenerating}
        size="lg"
        className="rounded-full font-bold text-xl px-10 py-7 shadow-lg transform hover:scale-105 transition-transform active:scale-100"
      >
        <WandSparkles
          className={cn("mr-3 h-7 w-7", isGenerating && "animate-spin")}
        />
        {isGenerating ? "Creating Magic..." : "Generate My Labubu"}
      </Button>
    </div>
  );
}
