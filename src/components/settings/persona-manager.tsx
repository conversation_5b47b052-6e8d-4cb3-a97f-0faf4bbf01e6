"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Trash2, Plus, User } from "lucide-react";
import { Persona } from "@/types/labubu";
import { DataManager } from "@/lib/data-manager";
import { useToast } from "@/hooks/use-toast";
import Image from "next/image";
import FileUpload from "@/components/ui/file-upload";

export default function PersonaManager() {
  const [personas, setPersonas] = useState<Persona[]>([]);
  const [isAdding, setIsAdding] = useState(false);
  const [newPersona, setNewPersona] = useState({
    name: "",
    personality: "",
    personalityKeywords: [] as string[],
    facts: [] as string[],
    description: "",
  });
  const [newKeyword, setNewKeyword] = useState("");
  const [newFact, setNewFact] = useState("");
  const { toast } = useToast();

  useEffect(() => {
    setPersonas(DataManager.getPersonas());
  }, []);

  const addKeyword = () => {
    if (newKeyword.trim() && !newPersona.personalityKeywords.includes(newKeyword.trim())) {
      setNewPersona({
        ...newPersona,
        personalityKeywords: [...newPersona.personalityKeywords, newKeyword.trim()],
      });
      setNewKeyword("");
    }
  };

  const removeKeyword = (index: number) => {
    setNewPersona({
      ...newPersona,
      personalityKeywords: newPersona.personalityKeywords.filter((_, i) => i !== index),
    });
  };

  const addFact = () => {
    if (newFact.trim() && !newPersona.facts.includes(newFact.trim())) {
      setNewPersona({
        ...newPersona,
        facts: [...newPersona.facts, newFact.trim()],
      });
      setNewFact("");
    }
  };

  const removeFact = (index: number) => {
    setNewPersona({
      ...newPersona,
      facts: newPersona.facts.filter((_, i) => i !== index),
    });
  };

  const handleAdd = () => {
    if (!newPersona.name.trim() || !newPersona.personality.trim() || newPersona.personalityKeywords.length === 0 || newPersona.facts.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please provide name, personality, at least one keyword, and at least one fact.",
        variant: "destructive",
      });
      return;
    }

    try {
      const added = DataManager.addPersona(newPersona);
      setPersonas(DataManager.getPersonas());
      setNewPersona({ name: "", personality: "", personalityKeywords: [], facts: [], description: "" });
      setNewKeyword("");
      setNewFact("");
      setIsAdding(false);
      toast({
        title: "Success",
        description: "Persona added successfully!",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add persona.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = (id: string) => {
    try {
      DataManager.deletePersona(id);
      setPersonas(DataManager.getPersonas());
      toast({
        title: "Success",
        description: "Persona deleted successfully!",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete persona.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Personas</h3>
        <Button
          onClick={() => setIsAdding(!isAdding)}
          size="sm"
          variant={isAdding ? "outline" : "default"}
        >
          <Plus className="w-4 h-4 mr-2" />
          {isAdding ? "Cancel" : "Add New"}
        </Button>
      </div>

      {isAdding && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Add New Persona</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="persona-name">Name</Label>
              <Input
                id="persona-name"
                value={newPersona.name}
                onChange={(e) => setNewPersona({ ...newPersona, name: e.target.value })}
                placeholder="e.g., Luna the Dreamer"
              />
            </div>
            <div>
              <Label htmlFor="personality">Personality</Label>
              <Textarea
                id="personality"
                value={newPersona.personality}
                onChange={(e) => setNewPersona({ ...newPersona, personality: e.target.value })}
                placeholder="Describe the personality traits and characteristics..."
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="keywords">Personality Keywords</Label>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    value={newKeyword}
                    onChange={(e) => setNewKeyword(e.target.value)}
                    placeholder="Add a keyword..."
                    onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                  />
                  <Button type="button" onClick={addKeyword} size="sm">Add</Button>
                </div>
                <div className="flex flex-wrap gap-1">
                  {newPersona.personalityKeywords.map((keyword, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      {keyword}
                      <button
                        type="button"
                        onClick={() => removeKeyword(index)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            </div>
            <div>
              <Label htmlFor="facts">Facts</Label>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    value={newFact}
                    onChange={(e) => setNewFact(e.target.value)}
                    placeholder="Add a fact about this persona..."
                    onKeyPress={(e) => e.key === 'Enter' && addFact()}
                  />
                  <Button type="button" onClick={addFact} size="sm">Add</Button>
                </div>
                <div className="space-y-1">
                  {newPersona.facts.map((fact, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-2 p-2 bg-gray-50 rounded text-sm"
                    >
                      <span className="flex-1">{fact}</span>
                      <button
                        type="button"
                        onClick={() => removeFact(index)}
                        className="text-red-600 hover:text-red-800 text-xs"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div>
              <Label htmlFor="persona-description">Description (Optional)</Label>
              <Input
                id="persona-description"
                value={newPersona.description}
                onChange={(e) => setNewPersona({ ...newPersona, description: e.target.value })}
                placeholder="Short description of this persona"
              />
            </div>
            <Button onClick={handleAdd} className="w-full">
              Add Persona
            </Button>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {personas.map((persona) => (
          <Card key={persona.id} className="relative">
            <CardContent className="p-4">
              <div className="flex items-start gap-3 mb-3">
                <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-100 flex-shrink-0">
                  <div className="w-full h-full flex items-center justify-center">
                    <User className="w-6 h-6 text-gray-400" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-sm mb-1">{persona.name}</h4>
                  {persona.description && (
                    <p className="text-xs text-muted-foreground mb-2">
                      {persona.description}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-3 mb-4">
                <div>
                  <p className="text-xs font-medium text-gray-600 mb-1">Personality:</p>
                  <p className="text-xs text-gray-700 line-clamp-2">{persona.personality}</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-600 mb-1">Keywords:</p>
                  <div className="flex flex-wrap gap-1">
                    {(persona.personalityKeywords || []).map((keyword, index) => (
                      <span
                        key={index}
                        className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                      >
                        {keyword}
                      </span>
                    ))}
                    {(!persona.personalityKeywords || persona.personalityKeywords.length === 0) && (
                      <span className="text-xs text-gray-500 italic">No keywords (legacy persona)</span>
                    )}
                  </div>
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-600 mb-1">Facts ({(persona.facts || []).length}):</p>
                  <div className="space-y-1 max-h-20 overflow-y-auto">
                    {(persona.facts || []).slice(0, 3).map((fact, index) => (
                      <p key={index} className="text-xs text-gray-700 line-clamp-1">
                        • {fact}
                      </p>
                    ))}
                    {(persona.facts || []).length > 3 && (
                      <p className="text-xs text-gray-500 italic">
                        +{(persona.facts || []).length - 3} more facts...
                      </p>
                    )}
                    {(!persona.facts || persona.facts.length === 0) && (
                      <span className="text-xs text-gray-500 italic">No facts (legacy persona)</span>
                    )}
                  </div>
                </div>
              </div>
              
              <Button
                onClick={() => handleDelete(persona.id)}
                size="sm"
                variant="destructive"
                className="w-full"
              >
                <Trash2 className="w-3 h-3 mr-1" />
                Delete
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {personas.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <User className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No personas yet. Add your first one!</p>
        </div>
      )}
    </div>
  );
}
