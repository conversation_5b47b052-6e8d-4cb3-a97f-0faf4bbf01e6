"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Settings, Users } from "lucide-react";
import PersonaManager from "./persona-manager";

interface SettingsDialogProps {
  children?: React.ReactNode;
}

export default function SettingsDialog({ children }: SettingsDialogProps) {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" size="icon">
            <Settings className="h-4 w-4" />
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Labubu Generator Settings
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <div className="flex items-center gap-2 mb-4 pb-2 border-b">
            <Users className="h-4 w-4" />
            <h3 className="font-medium">Personas</h3>
          </div>

          <div className="overflow-y-auto flex-1">
            <PersonaManager />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
