// src/ai/flows/generate-labubu-image.ts
'use server';

import OpenAI from 'openai';
import { writeFile } from 'fs/promises';
import { join } from 'path';
import { Persona, Background, GenerationResult } from '@/types/labubu';

interface GenerateLabubuImageInput {
  persona: Persona;
  background: Background;
  selectedFacts: string[];
}

export async function generateLabubuImage(input: GenerateLabubuImageInput): Promise<GenerationResult> {
  const openai = new OpenAI({
    baseURL: "https://openrouter.ai/api/v1",
    apiKey: process.env.OPENROUTER_API_KEY!,
  });

  const model = 'google/gemini-2.5-flash-image-preview';

  // Create a unified prompt using the new system
  const personalityKeywordsText = (input.persona.personalityKeywords || []).join(', ') || 'unique, special, charming';
  const selectedFactsText = input.selectedFacts.join('. ');

  const combinedPrompt = `
Create a unique Labubu character with the following specifications:

PERSONA: ${input.persona.name}
PERSONALITY: ${input.persona.personality}
KEY TRAITS: ${personalityKeywordsText}
SPECIAL FACTS: ${selectedFactsText}

BACKGROUND SETTING: ${input.background.prompt}

UNIFIED GENERATION REQUIREMENTS:
- Create a complete Labubu figure (small, toy-like, collectible character)
- The Labubu should be a full integrated character, not separate base + face
- Embody the personality through the character's design, pose, and accessories
- Include visual elements that reflect the special facts about this persona
- Place the character in the specified background setting
- Use colors and design elements that match both the personality and the background
- High quality, detailed, professional collectible toy photography style
- The character should look like a real collectible figure that could exist
- Make it whimsical and charming while staying true to the Labubu aesthetic

Also provide a short, whimsical description of this unique Labubu character, incorporating some of the special facts and explaining what makes it special (2-3 sentences).
`;

  // Prepare content parts for OpenAI format (text only, no images)
  const contentParts: any[] = [
    {
      type: "text",
      text: combinedPrompt,
    },
  ];

  const messages = [
    {
      role: 'user' as const,
      content: contentParts,
    },
  ];

  try {
    // Use type assertion for OpenRouter-specific modalities parameter
    const completion = await openai.chat.completions.create({
      model,
      messages,
      modalities: ["image", "text"], // Request both image and text output
    } as any);

    let generatedText = '';
    let imageBuffer: Buffer | null = null;
    let fileExtension = 'png';

    // Extract the response content
    const responseMessage = completion.choices[0]?.message as any;
    const responseContent = responseMessage?.content;

    if (typeof responseContent === 'string') {
      generatedText = responseContent;
    }

    // Check for generated images in the response (OpenRouter-specific)
    if (responseMessage?.images && responseMessage.images.length > 0) {
      const imageData = responseMessage.images[0];
      if (imageData.image_url?.url) {
        // Extract base64 data from data URL
        const dataUrl = imageData.image_url.url;
        if (dataUrl.startsWith('data:image/')) {
          const base64Data = dataUrl.split(',')[1];
          const mimeType = dataUrl.split(';')[0].split(':')[1];
          fileExtension = mimeType.split('/')[1] || 'png';
          imageBuffer = Buffer.from(base64Data, 'base64');
          console.log('✅ Image generated successfully!');
        }
      }
    }

    // For now, since we're not getting image data directly from the chat completion,
    // we'll return a fallback response with a placeholder image and generated text
    if (!imageBuffer) {
      console.log('No image was generated from the model, using fallback...');
      return {
        imageUrl: '/placeholder-labubu.svg', // Use a placeholder image as fallback
        text: generatedText.trim() || `Meet ${input.persona.name}! This unique creation embodies ${input.persona.personality.split('.')[0].toLowerCase()}. A truly special collectible that combines classic Labubu charm with ${input.persona.name}'s distinctive spirit.`,
        background: input.background,
        selectedFacts: input.selectedFacts,
      };
    }

    // Save the image to public directory (if we had image data)
    const fileName = `labubu-${Date.now()}-${Math.random().toString(36).substring(2, 11)}.${fileExtension}`;
    const publicPath = join(process.cwd(), 'public', 'generated', fileName);

    // Ensure the directory exists
    const { mkdir } = await import('fs/promises');
    const dir = join(process.cwd(), 'public', 'generated');
    try {
      await mkdir(dir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    await writeFile(publicPath, imageBuffer);

    const imageUrl = `/generated/${fileName}`;

    return {
      imageUrl,
      text: generatedText.trim() || `A unique ${input.persona.name} Labubu embodying ${input.persona.personality.split('.')[0].toLowerCase()}.`,
      background: input.background,
      selectedFacts: input.selectedFacts,
    };

  } catch (error) {
    console.error('Error generating Labubu image:', error); // Updated error handling

    // Check if it's a quota/rate limit error and provide a fallback
    const errorMessage = error instanceof Error ? error.message : String(error);
    const isQuotaError = errorMessage.includes('quota') ||
                        errorMessage.includes('429') ||
                        errorMessage.includes('RESOURCE_EXHAUSTED') ||
                        errorMessage.includes('Too Many Requests');

    if (isQuotaError) {
      console.log('Quota limit reached, using fallback...');
      // Return a fallback response with a placeholder image and a generated description
      return {
        imageUrl: '/placeholder-labubu.svg', // Use a placeholder image as fallback
        text: `Meet ${input.persona.name}! This unique creation embodies ${input.persona.personality.split('.')[0].toLowerCase()}. A truly special collectible that combines classic Labubu charm with ${input.persona.name}'s distinctive spirit. (Note: AI image generation temporarily unavailable due to quota limits - showing placeholder design)`,
        background: input.background,
        selectedFacts: input.selectedFacts,
      };
    }

    throw new Error(`Failed to generate Labubu image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
