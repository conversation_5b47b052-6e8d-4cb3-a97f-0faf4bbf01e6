// Types for the Labubu app generator

export interface BaseLububu {
  id: string;
  name: string;
  imageData: string; // base64 encoded image data
  imageType: string; // mime type (e.g., 'image/png', 'image/jpeg')
  description?: string;
  createdAt: Date;
}

export type BackgroundRarity = 'common' | 'rare' | 'epic' | 'legendary';

export interface Background {
  id: string;
  name: string;
  prompt: string;
  rarity: BackgroundRarity;
  weight: number; // Higher weight = more likely to be selected
}

export interface Persona {
  id: string;
  name: string;
  personality: string;
  personalityKeywords: string[]; // Keywords that describe the personality
  facts: string[]; // List of facts about this persona
  description?: string;
  createdAt: Date;
}

export interface GeneratedLububu {
  id: string;
  personaId: string;
  backgroundId: string;
  selectedFacts: string[]; // Subset of facts used in generation
  generatedImageUrl: string;
  generatedText: string;
  createdAt: Date;
}

export interface GenerationRequest {
  persona: Persona;
  background: Background;
  selectedFacts: string[];
}

export interface GenerationResult {
  imageUrl: string;
  text: string;
  background: Background;
  selectedFacts: string[];
}
