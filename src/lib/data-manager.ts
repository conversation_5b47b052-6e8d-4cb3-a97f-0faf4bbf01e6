import { BaseLububu, Persona } from '@/types/labubu';
import baseLububusData from '@/data/base-lububus.json';
import personasData from '@/data/personas.json';

// In a real app, this would be connected to a database
// For now, we'll use localStorage and the JSON files as defaults

const STORAGE_KEYS = {
  BASE_LUBUBUS: 'lububu-base-lububus',
  PERSONAS: 'lububu-personas',
} as const;

export class DataManager {
  // Base Lububus
  static getBaseLububus(): BaseLububu[] {
    if (typeof window === 'undefined') {
      return baseLububusData.map(item => ({
        ...item,
        createdAt: new Date(item.createdAt)
      }));
    }

    const stored = localStorage.getItem(STORAGE_KEYS.BASE_LUBUBUS);
    if (stored) {
      const parsed = JSON.parse(stored);
      return parsed.map((item: any) => ({
        ...item,
        createdAt: new Date(item.createdAt)
      }));
    }

    // Initialize with default data
    const defaultData = baseLububusData.map(item => ({
      ...item,
      createdAt: new Date(item.createdAt)
    }));
    this.saveBaseLububus(defaultData);
    return defaultData;
  }

  static saveBaseLububus(lububus: BaseLububu[]): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(STORAGE_KEYS.BASE_LUBUBUS, JSON.stringify(lububus));
  }

  static addBaseLububu(labubu: Omit<BaseLububu, 'id' | 'createdAt'>): BaseLububu {
    const newLububu: BaseLububu = {
      ...labubu,
      id: `base-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
    };

    const existing = this.getBaseLububus();
    const updated = [...existing, newLububu];
    this.saveBaseLububus(updated);
    return newLububu;
  }

  // Helper method to convert file to base64
  static async fileToBase64(file: File): Promise<{ data: string; type: string }> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve({
          data: result,
          type: file.type,
        });
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  static deleteBaseLububu(id: string): void {
    const existing = this.getBaseLububus();
    const updated = existing.filter(item => item.id !== id);
    this.saveBaseLububus(updated);
  }

  // Personas
  static getPersonas(): Persona[] {
    if (typeof window === 'undefined') {
      return personasData.map(item => ({
        ...item,
        createdAt: new Date(item.createdAt)
      }));
    }

    const stored = localStorage.getItem(STORAGE_KEYS.PERSONAS);
    if (stored) {
      const parsed = JSON.parse(stored);
      const personas = parsed.map((item: any) => ({
        ...item,
        createdAt: new Date(item.createdAt)
      }));

      // Migrate legacy personas to new format
      const migratedPersonas = this.migrateLegacyPersonas(personas);
      if (migratedPersonas.some((p, i) => p !== personas[i])) {
        this.savePersonas(migratedPersonas);
        return migratedPersonas;
      }

      return personas;
    }

    // Initialize with default data
    const defaultData = personasData.map(item => ({
      ...item,
      createdAt: new Date(item.createdAt)
    }));
    this.savePersonas(defaultData);
    return defaultData;
  }

  /**
   * Migrates legacy personas to the new format
   */
  private static migrateLegacyPersonas(personas: any[]): Persona[] {
    return personas.map(persona => {
      // If already has new format, return as is
      if (persona.personalityKeywords && persona.facts) {
        return persona;
      }

      // Migrate legacy persona
      const migrated = {
        ...persona,
        personalityKeywords: persona.personalityKeywords || ['unique', 'special', 'charming'],
        facts: persona.facts || [
          `${persona.name} has a mysterious past`,
          `Known for their unique personality`,
          `Carries special energy wherever they go`
        ]
      };

      // Remove old properties if they exist
      delete migrated.aiPrompt;
      delete migrated.faceImageData;
      delete migrated.faceImageType;

      return migrated;
    });
  }

  static savePersonas(personas: Persona[]): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(STORAGE_KEYS.PERSONAS, JSON.stringify(personas));
  }

  static addPersona(persona: Omit<Persona, 'id' | 'createdAt'>): Persona {
    const newPersona: Persona = {
      ...persona,
      id: `persona-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
    };

    const existing = this.getPersonas();
    const updated = [...existing, newPersona];
    this.savePersonas(updated);
    return newPersona;
  }

  static deletePersona(id: string): void {
    const existing = this.getPersonas();
    const updated = existing.filter(item => item.id !== id);
    this.savePersonas(updated);
  }

  // Utility methods
  static getRandomBaseLububu(): BaseLububu | null {
    const lububus = this.getBaseLububus();
    if (lububus.length === 0) return null;
    return lububus[Math.floor(Math.random() * lububus.length)];
  }

  static getRandomPersona(): Persona | null {
    const personas = this.getPersonas();
    if (personas.length === 0) return null;
    return personas[Math.floor(Math.random() * personas.length)];
  }

  /**
   * Selects a random subset of facts from a persona
   * @param persona The persona to select facts from
   * @param count Number of facts to select (default: 2-4 random)
   */
  static selectRandomFacts(persona: Persona, count?: number): string[] {
    if (!persona.facts || persona.facts.length === 0) {
      // For legacy personas without facts, generate some generic ones
      return [
        `${persona.name} has a mysterious past`,
        `Known for their unique ${persona.personality.split(' ')[0].toLowerCase()} nature`,
        `Carries the essence of their personality wherever they go`
      ];
    }

    // If no count specified, select 2-4 facts randomly
    const factCount = count || Math.floor(Math.random() * 3) + 2; // 2-4 facts
    const maxFacts = Math.min(factCount, persona.facts.length);

    // Shuffle and select
    const shuffled = [...persona.facts].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, maxFacts);
  }
}
