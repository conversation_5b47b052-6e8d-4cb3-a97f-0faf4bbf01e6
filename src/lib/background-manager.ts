import { Background, BackgroundRarity } from '@/types/labubu';
import backgroundsData from '@/data/backgrounds.json';

export class BackgroundManager {
  static getBackgrounds(): Background[] {
    return backgroundsData as Background[];
  }

  static getBackgroundById(id: string): Background | null {
    const backgrounds = this.getBackgrounds();
    return backgrounds.find(bg => bg.id === id) || null;
  }

  static getBackgroundsByRarity(rarity: BackgroundRarity): Background[] {
    const backgrounds = this.getBackgrounds();
    return backgrounds.filter(bg => bg.rarity === rarity);
  }

  /**
   * Selects a random background based on rarity weights
   * Higher weight = more likely to be selected
   */
  static getRandomBackground(): Background {
    const backgrounds = this.getBackgrounds();
    
    // Calculate total weight
    const totalWeight = backgrounds.reduce((sum, bg) => sum + bg.weight, 0);
    
    // Generate random number between 0 and totalWeight
    let random = Math.random() * totalWeight;
    
    // Find the background that corresponds to this random number
    for (const background of backgrounds) {
      random -= background.weight;
      if (random <= 0) {
        return background;
      }
    }
    
    // Fallback to first background (should never happen)
    return backgrounds[0];
  }

  /**
   * Get rarity color for UI display
   */
  static getRarityColor(rarity: BackgroundRarity): string {
    switch (rarity) {
      case 'common':
        return '#9CA3AF'; // gray-400
      case 'rare':
        return '#3B82F6'; // blue-500
      case 'epic':
        return '#8B5CF6'; // violet-500
      case 'legendary':
        return '#F59E0B'; // amber-500
      default:
        return '#9CA3AF';
    }
  }

  /**
   * Get rarity display name
   */
  static getRarityDisplayName(rarity: BackgroundRarity): string {
    switch (rarity) {
      case 'common':
        return 'Common';
      case 'rare':
        return 'Rare';
      case 'epic':
        return 'Epic';
      case 'legendary':
        return 'Legendary';
      default:
        return 'Unknown';
    }
  }

  /**
   * Get rarity probability percentage for display
   */
  static getRarityProbability(rarity: BackgroundRarity): number {
    const backgrounds = this.getBackgrounds();
    const totalWeight = backgrounds.reduce((sum, bg) => sum + bg.weight, 0);
    const rarityWeight = backgrounds
      .filter(bg => bg.rarity === rarity)
      .reduce((sum, bg) => sum + bg.weight, 0);
    
    return Math.round((rarityWeight / totalWeight) * 100);
  }
}
