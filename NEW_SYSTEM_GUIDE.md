# New Labubu Generation System

## Overview
The Labubu generation system has been completely redesigned to be simpler, more unified, and more exciting with a FIFA-card style rarity system.

## Key Changes

### 1. Simplified Persona Structure
**Before:**
- Personas had separate `aiPrompt` and `faceImageData`
- Complex base Labubu + face combination system

**Now:**
- Personas have `personalityKeywords` (array of descriptive words)
- Personas have `facts` (array of unique characteristics)
- Single unified generation system

### 2. Background Rarity System
- **Common (40% chance)**: Basic everyday settings
- **Rare (20% chance)**: Magical and mystical environments  
- **Epic (8% chance)**: Extraordinary fantasy locations
- **Legendary (2% chance)**: Divine and cosmic realms

### 3. Generation Process
1. **Random Selection**: Pick a random persona and background
2. **Fact Selection**: Choose 2-4 random facts from the persona
3. **Unified Generation**: Create complete integrated Labubu PNG
4. **Display**: Show rarity, selected facts, and generated description

## How to Use

### Adding New Personas
1. Open Settings (gear icon)
2. Click "Add New" in Personas section
3. Fill in:
   - **Name**: The persona's name
   - **Personality**: Description of their character
   - **Keywords**: Add descriptive words one by one
   - **Facts**: Add unique characteristics one by one
   - **Description**: Optional short description

### Generation Features
- Each generation shows the background rarity with colored badges
- Selected facts are displayed in the fortune area
- Backgrounds are automatically selected based on rarity weights
- No user control over backgrounds (they're surprises!)

## Technical Details

### File Structure
- `src/types/labubu.ts` - Updated type definitions
- `src/data/backgrounds.json` - Background definitions with rarities
- `src/data/personas.json` - Updated persona format
- `src/lib/background-manager.ts` - Background selection logic
- `src/lib/data-manager.ts` - Persona management and fact selection
- `src/ai/flows/generate-labubu-image.ts` - Unified generation system

### Migration
- Legacy personas are automatically migrated to new format
- Old `aiPrompt` and `faceImageData` fields are removed
- Default keywords and facts are added for legacy personas

## Background Rarity Details

| Rarity | Weight | Probability | Examples |
|--------|--------|-------------|----------|
| Common | 40 | ~67% | Cozy Room, Garden Path |
| Rare | 20 | ~27% | Starlit Meadow, Crystal Cave |
| Epic | 8 | ~5% | Aurora Palace, Dragon's Lair |
| Legendary | 2 | ~1% | Celestial Throne, Universe Heart |

The system creates excitement through the rarity system while maintaining the core Labubu charm!
